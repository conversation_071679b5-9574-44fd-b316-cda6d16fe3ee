from typing import Any, Dict
from app.db.connection import get_connection


# def fetch_full_policy_details(policy_id: int) -> Dict:
#     conn = get_connection()
#     cursor = conn.cursor(dictionary=True)

#     try:
#         # 📌 Main query to fetch policy + related entities
#         cursor.execute("""
#             SELECT 
#     -- POLICY
#     p.POLICY_ID, p.CUSTOMER_ID, p.INSURANCE_COMPANY_ID, p.AGENT_ID, p.INSURANCE_PRODUCT_CODE,
#     p.POLICY_TYPE, p.POLICY_ISSUED_DATE, p.POLICY_EXPIRY_DATE, p.POLICY_STATUS,
#     p.PREMIUM_AMOUNT, p.FACE_AMOUNT, p.LOAN_AMOUNT_DISBURESED,
#     p.MINIMUM_INTEREST_RATE_IN_PERCENTAGE, p.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE,
#     p.CURRENT_INTEREST_RATE_IN_PERCENTAGE, p.WITHDRAWL_AMOUNT, p.RIDER_APPLICABLE,
#     p.POLICY_TERM_YEARS, p.CURRENT_CASH_VALUE,

#     -- CUSTOMER
#     c.CUSTOMER_FIRST_NAME, c.CUSTOMER_MIDDLE_NAME, c.CUSTOMER_LAST_NAME,
#     c.SALUTATION, c.GENDER, c.DATE_OF_BIRTH, c.CONTACT_NUMBER, c.EMAIL,
#     c.ADDRESS_LINE_1 AS CUSTOMER_ADDRESS_LINE_1, c.ADDRESS_LINE_2 AS CUSTOMER_ADDRESS_LINE_2,
#     c.CITY AS CUSTOMER_CITY, c.STATE AS CUSTOMER_STATE, c.ZIP_CODE AS CUSTOMER_ZIP,
#     c.COUNTRY AS CUSTOMER_COUNTRY,

#     -- AGENT
#     a.AGENT_ID, a.AGENT_CODE, a.SALUTATION AS AGENT_SALUTATION,
#     a.AGENT_FIRST_NAME, a.AGENT_MIDDLE_NAME, a.AGENT_LAST_NAME, a.AGENT_NAME,
#     a.AGENT_GENDER, a.AGENT_EMAIL, a.AGENT_PHONE_NUMBER,
#     a.AGENT_STATE, a.AGENT_STATUS,

#     -- PRODUCT
#     pr.INSURANCE_PRODUCT_ID, pr.INSURANCE_PRODUCT_CODE, pr.INSURANCE_PRODUCT_NAME,
#     pr.INSURANCE_PRODUCT_LINE_OF_BUSINESS, pr.INSURANCE_PRODUCT_DESCRIPTION,
#     pr.INSURANCE_PRODUCT_STATUS, pr.INSURANCE_PRODUCT_EXPIRY_DATE,

#     -- COMPANY
#     ic.INSURANCE_COMPANY_NAME,
#     ic.CONTACT_NUMBER AS COMPANY_PHONE, ic.EMAIL AS COMPANY_EMAIL,
#     ic.ADDRESS_LINE_1 AS COMPANY_ADDRESS_LINE_1, ic.ADDRESS_LINE_2 AS COMPANY_ADDRESS_LINE_2,
#     ic.CITY AS COMPANY_CITY, ic.STATE AS COMPANY_STATE, ic.ZIP_CODE AS COMPANY_ZIP,
#     ic.COUNTRY AS COMPANY_COUNTRY

# FROM INS_POLICY p
# JOIN INS_CUSTOMER c ON p.CUSTOMER_ID = c.CUSTOMER_ID
# JOIN INS_INSURANCE_AGENT a ON p.AGENT_ID = a.AGENT_ID
# JOIN INS_INSURANCE_PRODUCT pr ON p.PRODUCT_CODE = pr.INSURANCE_PRODUCT_CODE
# JOIN INS_INSURANCE_COMPANY ic ON p.INSURANCE_COMPANY_ID = ic.INSURANCE_COMPANY_ID
# WHERE p.POLICY_ID = %s;
#         """, (policy_id,))
#         policy_data = cursor.fetchone()

#         if not policy_data:
#             raise Exception("Policy not found.")

#         # 🔄 Fetch nested data
#         def fetch_related(query, params):
#             cursor.execute(query, params)
#             return cursor.fetchall()

#         policy_data["riders"] = fetch_related("SELECT * FROM INS_RIDER WHERE POLICY_ID = %s", (policy_id,))
#         policy_data["beneficiaries"] = fetch_related("SELECT * FROM INS_BENEFICIARY WHERE POLICY_ID = %s", (policy_id,))
#         policy_data["transactions"] = fetch_related("SELECT * FROM INS_TRANSACTION WHERE POLICY_ID = %s", (policy_id,))
#         policy_data["loans"] = []

#         cursor.execute("SELECT * FROM INS_LOAN WHERE POLICY_ID = %s", (policy_id,))
#         loans = cursor.fetchall()

#         for loan in loans:
#             cursor.execute("SELECT * FROM INS_SCHEDULE_REPAYMENT WHERE LOAN_ID = %s", (loan["LOAN_ID"],))
#             loan["repayments"] = cursor.fetchall()
#             policy_data["loans"].append(loan)

#         return policy_data

#     finally:
#         cursor.close()
#         conn.close()
def fetch_full_policy_details(policy_id: int) -> Dict:
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 📌 Main query to fetch policy + related entities
        cursor.execute("""
            SELECT 
    -- POLICY
    p.POLICY_ID, p.CUSTOMER_ID, p.INSURANCE_COMPANY_ID, p.AGENT_ID, p.INSURANCE_PRODUCT_CODE,
    p.POLICY_TYPE, p.POLICY_ISSUED_DATE, p.POLICY_EXPIRY_DATE, p.POLICY_STATUS,
    p.PREMIUM_AMOUNT, p.FACE_AMOUNT, p.LOAN_AMOUNT_DISBURESED,
    p.MINIMUM_INTEREST_RATE_IN_PERCENTAGE, p.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE,
    p.CURRENT_INTEREST_RATE_IN_PERCENTAGE, p.WITHDRAWL_AMOUNT, p.RIDER_APPLICABLE,
    p.POLICY_TERM_YEARS, p.CURRENT_CASH_VALUE,

    -- CUSTOMER
    c.CUSTOMER_FIRST_NAME, c.CUSTOMER_MIDDLE_NAME, c.CUSTOMER_LAST_NAME,
    c.SALUTATION, c.GENDER, c.DATE_OF_BIRTH, c.CONTACT_NUMBER, c.EMAIL,
    c.ADDRESS_LINE_1 AS CUSTOMER_ADDRESS_LINE_1, c.ADDRESS_LINE_2 AS CUSTOMER_ADDRESS_LINE_2,
    c.CITY AS CUSTOMER_CITY, c.STATE AS CUSTOMER_STATE, c.ZIP_CODE AS CUSTOMER_ZIP,
    c.COUNTRY AS CUSTOMER_COUNTRY,

    -- AGENT
    a.AGENT_ID, a.AGENT_CODE, a.SALUTATION AS AGENT_SALUTATION,
    a.AGENT_FIRST_NAME, a.AGENT_MIDDLE_NAME, a.AGENT_LAST_NAME, a.AGENT_NAME,
    a.AGENT_GENDER, a.AGENT_EMAIL, a.AGENT_PHONE_NUMBER,
    a.AGENT_STATE, a.AGENT_STATUS,

    -- PRODUCT
    pr.INSURANCE_PRODUCT_ID, pr.INSURANCE_PRODUCT_CODE, pr.INSURANCE_PRODUCT_NAME,
    pr.INSURANCE_PRODUCT_LINE_OF_BUSINESS, pr.INSURANCE_PRODUCT_DESCRIPTION,
    pr.INSURANCE_PRODUCT_STATUS, pr.INSURANCE_PRODUCT_EXPIRY_DATE,

    -- COMPANY
    ic.INSURANCE_COMPANY_NAME,
    ic.CONTACT_NUMBER AS COMPANY_PHONE, ic.EMAIL AS COMPANY_EMAIL,
    ic.ADDRESS_LINE_1 AS COMPANY_ADDRESS_LINE_1, ic.ADDRESS_LINE_2 AS COMPANY_ADDRESS_LINE_2,
    ic.CITY AS COMPANY_CITY, ic.STATE AS COMPANY_STATE, ic.ZIP_CODE AS COMPANY_ZIP,
    ic.COUNTRY AS COMPANY_COUNTRY

FROM INS_POLICY p
JOIN INS_CUSTOMER c ON p.CUSTOMER_ID = c.CUSTOMER_ID
JOIN INS_INSURANCE_AGENT a ON p.AGENT_ID = a.AGENT_ID
JOIN INS_INSURANCE_PRODUCT pr ON p.INSURANCE_PRODUCT_CODE = pr.INSURANCE_PRODUCT_CODE
JOIN INS_INSURANCE_COMPANY ic ON p.INSURANCE_COMPANY_ID = ic.INSURANCE_COMPANY_ID
WHERE p.POLICY_ID = %s;
        """, (policy_id,))
        policy_data = cursor.fetchone()

        if not policy_data:
            raise Exception("Policy not found.")

        # 🔄 Fetch nested data
        def fetch_related(query, params):
            cursor.execute(query, params)
            return cursor.fetchall()

        policy_data["riders"] = fetch_related("SELECT * FROM INS_RIDER WHERE POLICY_ID = %s", (policy_id,))
        policy_data["beneficiaries"] = fetch_related("SELECT * FROM INS_BENEFICIARY WHERE POLICY_ID = %s", (policy_id,))
        policy_data["transactions"] = fetch_related("SELECT * FROM INS_TRANSACTION WHERE POLICY_ID = %s", (policy_id,))
        policy_data["loans"] = []

        cursor.execute("SELECT * FROM INS_LOAN WHERE POLICY_ID = %s", (policy_id,))
        loans = cursor.fetchall()

        for loan in loans:
            cursor.execute("SELECT * FROM INS_SCHEDULE_REPAYMENT WHERE LOAN_ID = %s", (loan["LOAN_ID"],))
            loan["repayments"] = cursor.fetchall()
            policy_data["loans"].append(loan)

        return policy_data

    finally:
        cursor.close()
        conn.close()

# ✅ Store full policy data into ILL_* tables
# def store_policy_in_ill_tables(policy: Dict[str, Any]):
#     conn = get_connection()
#     cursor = conn.cursor()
#     print("[MAIN DB] MySQL connection successful!")

#     try:
#         policy_id = policy["POLICY_ID"]
#         agent_id = policy["AGENT_ID"]
#         # customer_id = policy["CUSTOMER_ID"]
#         product_id = policy["INSURANCE_PRODUCT_ID"]
#         product_code=policy["INSURANCE_PRODUCT_CODE"]
#         company_id = policy["INSURANCE_COMPANY_ID"]

#         # 🔄 Delete existing policy-related child records (if any)
#         print("🧹 Cleaning up previous data for POLICY_ID:", policy_id)
#         cursor.execute("DELETE FROM ILL_SCHEDULE_REPAYMENT WHERE LOAN_ID IN (SELECT LOAN_ID FROM ILL_LOAN WHERE POLICY_ID = %s)", (policy_id,))
#         cursor.execute("DELETE FROM ILL_LOAN WHERE POLICY_ID = %s", (policy_id,))
#         cursor.execute("DELETE FROM ILL_TRANSACTION WHERE POLICY_ID = %s", (policy_id,))
#         cursor.execute("DELETE FROM ILL_BENEFICIARY WHERE POLICY_ID = %s", (policy_id,))
#         cursor.execute("DELETE FROM ILL_RIDER WHERE POLICY_ID = %s", (policy_id,))
#         cursor.execute("DELETE FROM ILL_POLICY WHERE POLICY_ID = %s", (policy_id,))

#         # 🔄 Delete master records (safe only if you control the ILL database fully)
#         print("🧹 Cleaning up master entries...")
#         # cursor.execute("DELETE FROM ILL_CUSTOMER WHERE CUSTOMER_ID = %s", (customer_id,))
#         cursor.execute("DELETE FROM ILL_INSURANCE_AGENT WHERE AGENT_ID = %s", (agent_id,))
#         # cursor.execute("DELETE FROM ILL_INSURANCE_PRODUCT WHERE INSURANCE_PRODUCT_CODE = %s", (product_code,))
#         cursor.execute("DELETE FROM ILL_INSURANCE_COMPANY WHERE INSURANCE_COMPANY_ID = %s", (company_id,))

#         # ✅ Re-insert master data
#         print("🏢 Reinserting ILL_INSURANCE_COMPANY...")
#         cursor.execute("""INSERT INTO ILL_INSURANCE_COMPANY (
#             INSURANCE_COMPANY_ID, INSURANCE_COMPANY_NAME, CONTACT_NUMBER,
#             ADDRESS_LINE_1, ADDRESS_LINE_2, CITY, STATE, ZIP_CODE, COUNTRY, EMAIL
#         ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""", (
#             company_id, policy.get("INSURANCE_COMPANY_NAME"), policy.get("COMPANY_PHONE"),
#             policy.get("COMPANY_ADDRESS_LINE_1"), policy.get("COMPANY_ADDRESS_LINE_2"),
#             policy.get("COMPANY_CITY"), policy.get("COMPANY_STATE"),
#             policy.get("COMPANY_ZIP"), policy.get("COMPANY_COUNTRY"),
#             policy.get("COMPANY_EMAIL")
#         ))

#         print("📦 Reinserting ILL_INSURANCE_PRODUCT...")
#         cursor.execute("""INSERT INTO ILL_INSURANCE_PRODUCT (
#             INSURANCE_PRODUCT_ID, INSURANCE_COMPANY_ID, INSURANCE_PRODUCT_NAME,
#             INSURANCE_PRODUCT_CODE, INSURANCE_PRODUCT_DESCRIPTION,
#             INSURANCE_PRODUCT_STATUS, INSURANCE_PRODUCT_EXPIRY_DATE,
#             INSURANCE_PRODUCT_LINE_OF_BUSINESS
#         ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""", (
#             product_code, company_id, policy.get("INSURANCE_PRODUCT_NAME"),
#             policy.get("INSURANCE_PRODUCT_CODE"), policy.get("INSURANCE_PRODUCT_DESCRIPTION"),
#             policy.get("INSURANCE_PRODUCT_STATUS"), policy.get("INSURANCE_PRODUCT_EXPIRY_DATE"),
#             policy.get("INSURANCE_PRODUCT_LINE_OF_BUSINESS")
#         ))

#         cursor.execute("""
#     INSERT INTO ILL_INSURANCE_AGENT (
#         AGENT_ID,
#         INSURANCE_COMPANY_ID,
#         AGENT_CODE,
#         SALUTATION,
#         AGENT_FIRST_NAME,
#         AGENT_MIDDLE_NAME,
#         AGENT_LAST_NAME,
#         AGENT_NAME,
#         AGENT_GENDER,
#         AGENT_EMAIL,
#         AGENT_PHONE_NUMBER,
#         AGENT_STATE,
#         AGENT_STATUS
#     ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
#     ON DUPLICATE KEY UPDATE
#         INSURANCE_COMPANY_ID = VALUES(INSURANCE_COMPANY_ID),
#         AGENT_CODE = VALUES(AGENT_CODE),
#         SALUTATION = VALUES(SALUTATION),
#         AGENT_FIRST_NAME = VALUES(AGENT_FIRST_NAME),
#         AGENT_MIDDLE_NAME = VALUES(AGENT_MIDDLE_NAME),
#         AGENT_LAST_NAME = VALUES(AGENT_LAST_NAME),
#         AGENT_NAME = VALUES(AGENT_NAME),
#         AGENT_GENDER = VALUES(AGENT_GENDER),
#         AGENT_EMAIL = VALUES(AGENT_EMAIL),
#         AGENT_PHONE_NUMBER = VALUES(AGENT_PHONE_NUMBER),
#         AGENT_STATE = VALUES(AGENT_STATE),
#         AGENT_STATUS = VALUES(AGENT_STATUS)
# """, (
#     policy["AGENT_ID"],
#     policy["INSURANCE_COMPANY_ID"],
#     policy.get("AGENT_CODE"),
#     policy.get("AGENT_SALUTATION"),
#     policy.get("AGENT_FIRST_NAME"),
#     policy.get("AGENT_MIDDLE_NAME"),
#     policy.get("AGENT_LAST_NAME"),
#     policy.get("AGENT_NAME"),
#     policy.get("AGENT_GENDER"),
#     policy.get("AGENT_EMAIL"),
#     policy.get("AGENT_PHONE_NUMBER"),
#     policy.get("AGENT_STATE"),
#     policy.get("AGENT_STATUS")
# ))

#         print("👤 Reinserting ILL_CUSTOMER...")
#         cursor.execute("""INSERT INTO ILL_CUSTOMER (
#             CUSTOMER_ID, INSURANCE_COMPANY_ID, CUSTOMER_FIRST_NAME,
#             CUSTOMER_MIDDLE_NAME, CUSTOMER_LAST_NAME, SALUTATION,
#             GENDER, DATE_OF_BIRTH, CONTACT_NUMBER, ADDRESS_LINE_1,
#             ADDRESS_LINE_2, CITY, STATE, ZIP_CODE, COUNTRY, EMAIL
#         ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""", (
#             customer_id, company_id, policy.get("CUSTOMER_FIRST_NAME"),
#             policy.get("CUSTOMER_MIDDLE_NAME"), policy.get("CUSTOMER_LAST_NAME"),
#             policy.get("SALUTATION"), policy.get("GENDER"), policy.get("DATE_OF_BIRTH"),
#             policy.get("CONTACT_NUMBER"), policy.get("CUSTOMER_ADDRESS_LINE_1"),
#             policy.get("CUSTOMER_ADDRESS_LINE_2"), policy.get("CUSTOMER_CITY"),
#             policy.get("CUSTOMER_STATE"), policy.get("CUSTOMER_ZIP"),
#             policy.get("CUSTOMER_COUNTRY"), policy.get("EMAIL")
#         ))

#         # ✅ Insert into ILL_POLICY
#         print("📄 Reinserting ILL_POLICY...")
#         cursor.execute("""INSERT INTO ILL_POLICY (
#             POLICY_ID, POLICY_TYPE, POLICY_ISSUED_DATE, POLICY_EXPIRY_DATE,
#             POLICY_STATUS, CUSTOMER_ID, INSURANCE_COMPANY_ID, AGENT_ID,INSURANCE_PRODUCT_ID
#             INSURANCE_PRODUCT_CODE, PREMIUM_AMOUNT, LOAN_AMOUNT_DISBURESED, FACE_AMOUNT,
#             MINIMUM_INTEREST_RATE_IN_PERCENTAGE, GUARANTEED_INTEREST_RATE_IN_PERCENTAGE,
#             CURRENT_INTEREST_RATE_IN_PERCENTAGE, WITHDRAWL_AMOUNT, RIDER_APPLICABLE,
#             POLICY_TERM_YEARS, CURRENT_CASH_VALUE
#         ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s)""", (
#             policy_id, policy.get("POLICY_TYPE"), policy.get("POLICY_ISSUED_DATE"),
#             policy.get("POLICY_EXPIRY_DATE"), policy.get("POLICY_STATUS"),
#             customer_id, company_id, agent_id, product_id,product_code
#             policy.get("PREMIUM_AMOUNT"), policy.get("LOAN_AMOUNT_DISBURESED"),
#             policy.get("FACE_AMOUNT"), policy.get("MINIMUM_INTEREST_RATE_IN_PERCENTAGE"),
#             policy.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE"),
#             policy.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE"),
#             policy.get("WITHDRAWL_AMOUNT"), policy.get("RIDER_APPLICABLE"),
#             policy.get("POLICY_TERM_YEARS"), policy.get("CURRENT_CASH_VALUE")
#         ))

#         # 🔁 Insert Riders, Beneficiaries, Transactions, Loans & Repayments
#         for rider in policy.get("riders", []):
#             cursor.execute("""INSERT INTO ILL_RIDER (
#                 RIDER_ID, POLICY_ID, RIDER_NAME, RIDER_ISSUE_DATE,
#                 RIDER_COVERAGE_AMOUNT, RIDER_PREMIUM_AMOUNT, RIDER_STATUS
#             ) VALUES (%s, %s, %s, %s, %s, %s, %s)""", (
#                 rider["RIDER_ID"], policy_id, rider.get("RIDER_NAME"),
#                 rider.get("RIDER_ISSUE_DATE"), rider.get("RIDER_COVERAGE_AMOUNT"),
#                 rider.get("RIDER_PREMIUM_AMOUNT"), rider.get("RIDER_STATUS")
#             ))

#         for ben in policy.get("beneficiaries", []):
#             cursor.execute("""INSERT INTO ILL_BENEFICIARY (
#                 BENEFICIARY_ID, POLICY_ID, BENEFICIARY_FIRST_NAME,
#                 BENEFICIARY_MIDDLE_NAME, BENEFICIARY_LAST_NAME,
#                 SALUTATION, BENEFICIARY_GENDER, BENEFICIARY_RELATIONSHIP
#             ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""", (
#                 ben["BENEFICIARY_ID"], policy_id, ben.get("BENEFICIARY_FIRST_NAME"),
#                 ben.get("BENEFICIARY_MIDDLE_NAME"), ben.get("BENEFICIARY_LAST_NAME"),
#                 ben.get("SALUTATION"), ben.get("BENEFICIARY_GENDER"),
#                 ben.get("BENEFICIARY_RELATIONSHIP")
#             ))

#         for txn in policy.get("transactions", []):
#             cursor.execute("""INSERT INTO ILL_TRANSACTION (
#                 TRANSACTION_ID, POLICY_ID, TRANSACTION_TYPE,
#                 TRANSACTION_DATE, TRANSACTION_STATUS, TRANSACTION_AMOUNT, TRANSACTION_REQUEST_BY
#             ) VALUES (%s, %s, %s, %s, %s, %s, %s)""", (
#                 txn["TRANSACTION_ID"], policy_id, txn.get("TRANSACTION_TYPE"),
#                 txn.get("TRANSACTION_DATE"), txn.get("TRANSACTION_STATUS"),
#                 txn.get("TRANSACTION_AMOUNT"), txn.get("TRANSACTION_REQUEST_BY")
#             ))

#         for loan in policy.get("loans", []):
#             cursor.execute("""INSERT INTO ILL_LOAN (
#                 LOAN_ID, POLICY_ID, LOAN_AMOUNT, LOAN_ISSUED_DATE,
#                 LOAN_PERIOD_IN_MONTHS, LOAN_INTEREST_RATE_IN_PERCENTAGE, LOAN_STATUS
#             ) VALUES (%s, %s, %s, %s, %s, %s, %s)""", (
#                 loan["LOAN_ID"], policy_id, loan.get("LOAN_AMOUNT"),
#                 loan.get("LOAN_ISSUED_DATE"), loan.get("LOAN_PERIOD_IN_MONTHS"),
#                 loan.get("LOAN_INTEREST_RATE_IN_PERCENTAGE"), loan.get("LOAN_STATUS")
#             ))

#             for repay in loan.get("repayments", []):
#                 cursor.execute("""INSERT INTO ILL_SCHEDULE_REPAYMENT (
#                     LOAN_ID, SCHEDULE_TYPE, LOAN_SCHEDULED_DUE_DATE,
#                     LOAN_ACTUAL_REPAYMENT_DATE, LOAN_REPAYMENT_AMOUNT, STATUS
#                 ) VALUES (%s, %s, %s, %s, %s, %s)""", (
#                     loan["LOAN_ID"], repay.get("SCHEDULE_TYPE"),
#                     repay.get("LOAN_SCHEDULED_DUE_DATE"), repay.get("LOAN_ACTUAL_REPAYMENT_DATE"),
#                     repay.get("LOAN_REPAYMENT_AMOUNT"), repay.get("STATUS")
#                 ))

#         conn.commit()
#         print("✅ Policy and all related data inserted successfully.")

#     except Exception as e:
#         print("❌ Error while inserting:", e)
#         conn.rollback()
#     finally:
#         cursor.close()
#         conn.close()
def store_policy_in_ill_tables(policy: dict):

    conn = get_connection()
    cursor = conn.cursor()
    try:
        policy_id = policy["POLICY_ID"]
        product_code = policy["INSURANCE_PRODUCT_CODE"]
        product_id = policy["INSURANCE_PRODUCT_ID"]
        company_id = policy["INSURANCE_COMPANY_ID"]
        # customer_id=policy["CUSTOMER_ID"]
        agent_id=policy["AGENT_ID"]
        print(f"🧹 Cleaning up previous data for POLICY_ID: {policy_id}")



        # 🔄 Delete existing policy-related child records (if any)
        print("🧹 Cleaning up previous data for POLICY_ID:", policy_id)
        cursor.execute("DELETE FROM ILL_SCHEDULE_REPAYMENT WHERE LOAN_ID IN (SELECT LOAN_ID FROM ILL_LOAN WHERE POLICY_ID = %s)", (policy_id,))
        cursor.execute("DELETE FROM ILL_LOAN WHERE POLICY_ID = %s", (policy_id,))
        cursor.execute("DELETE FROM ILL_TRANSACTION WHERE POLICY_ID = %s", (policy_id,))
        cursor.execute("DELETE FROM ILL_BENEFICIARY WHERE POLICY_ID = %s", (policy_id,))
        cursor.execute("DELETE FROM ILL_RIDER WHERE POLICY_ID = %s", (policy_id,))
        cursor.execute("DELETE FROM ILL_POLICY WHERE POLICY_ID = %s", (policy_id,))

        # 🔄 Delete master records (safe only if you control the ILL database fully)
        print("🧹 Cleaning up master entries...")
        # cursor.execute("DELETE FROM ILL_CUSTOMER WHERE INSURANCE_COMPANY_ID = %s", (company_id,))
        cursor.execute("DELETE FROM ILL_INSURANCE_AGENT WHERE AGENT_ID = %s", (agent_id,))
        # cursor.execute("DELETE FROM ILL_INSURANCE_PRODUCT WHERE INSURANCE_PRODUCT_ID = %s", (product_id,))
        cursor.execute("DELETE FROM ILL_INSURANCE_COMPANY WHERE INSURANCE_COMPANY_ID = %s", (company_id,))

        # Insert new entries
        print("✅ Inserting cleaned master entries...")

        # ILL_INSURANCE_COMPANY
        cursor.execute("""INSERT INTO ILL_INSURANCE_COMPANY (
            INSURANCE_COMPANY_ID, INSURANCE_COMPANY_NAME, CONTACT_NUMBER, EMAIL,
            ADDRESS_LINE_1, ADDRESS_LINE_2, CITY,
            STATE, ZIP_CODE, COUNTRY
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""", (
            company_id, policy.get("INSURANCE_COMPANY_NAME"), policy.get("COMPANY_PHONE"),
            policy.get("COMPANY_EMAIL"), policy.get("COMPANY_ADDRESS_LINE_1"),
            policy.get("COMPANY_ADDRESS_LINE_2"), policy.get("COMPANY_CITY"),
            policy.get("COMPANY_STATE"), policy.get("COMPANY_ZIP"),
            policy.get("COMPANY_COUNTRY")
        ))

        # ILL_INSURANCE_PRODUCT
        cursor.execute("""
    INSERT INTO ILL_INSURANCE_PRODUCT (
        INSURANCE_PRODUCT_ID, INSURANCE_COMPANY_ID, INSURANCE_PRODUCT_NAME,
        INSURANCE_PRODUCT_CODE, INSURANCE_PRODUCT_DESCRIPTION,
        INSURANCE_PRODUCT_STATUS, INSURANCE_PRODUCT_EXPIRY_DATE,
        INSURANCE_PRODUCT_LINE_OF_BUSINESS
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        INSURANCE_COMPANY_ID = VALUES(INSURANCE_COMPANY_ID),
        INSURANCE_PRODUCT_NAME = VALUES(INSURANCE_PRODUCT_NAME),
        INSURANCE_PRODUCT_CODE = VALUES(INSURANCE_PRODUCT_CODE),
        INSURANCE_PRODUCT_DESCRIPTION = VALUES(INSURANCE_PRODUCT_DESCRIPTION),
        INSURANCE_PRODUCT_STATUS = VALUES(INSURANCE_PRODUCT_STATUS),
        INSURANCE_PRODUCT_EXPIRY_DATE = VALUES(INSURANCE_PRODUCT_EXPIRY_DATE),
        INSURANCE_PRODUCT_LINE_OF_BUSINESS = VALUES(INSURANCE_PRODUCT_LINE_OF_BUSINESS)
""", (
    product_id, company_id, policy.get("INSURANCE_PRODUCT_NAME"),
    policy.get("INSURANCE_PRODUCT_CODE"), policy.get("INSURANCE_PRODUCT_DESCRIPTION"),
    policy.get("INSURANCE_PRODUCT_STATUS"), policy.get("INSURANCE_PRODUCT_EXPIRY_DATE"),
    policy.get("INSURANCE_PRODUCT_LINE_OF_BUSINESS")
))

        # ILL_CUSTOMER
        # ILL_CUSTOMER - Insert or update if already present
        cursor.execute("""
            INSERT INTO ILL_CUSTOMER ( 
                CUSTOMER_ID, INSURANCE_COMPANY_ID, SALUTATION, CUSTOMER_FIRST_NAME, CUSTOMER_MIDDLE_NAME, CUSTOMER_LAST_NAME,
                GENDER, DATE_OF_BIRTH, CONTACT_NUMBER, EMAIL,
                ADDRESS_LINE_1, ADDRESS_LINE_2, CITY, STATE, ZIP_CODE, COUNTRY
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                INSURANCE_COMPANY_ID = VALUES(INSURANCE_COMPANY_ID),
                SALUTATION = VALUES(SALUTATION),
                CUSTOMER_FIRST_NAME = VALUES(CUSTOMER_FIRST_NAME),
                CUSTOMER_MIDDLE_NAME = VALUES(CUSTOMER_MIDDLE_NAME),
                CUSTOMER_LAST_NAME = VALUES(CUSTOMER_LAST_NAME),
                GENDER = VALUES(GENDER),
                DATE_OF_BIRTH = VALUES(DATE_OF_BIRTH),
                CONTACT_NUMBER = VALUES(CONTACT_NUMBER),
                EMAIL = VALUES(EMAIL),
                ADDRESS_LINE_1 = VALUES(ADDRESS_LINE_1),
                ADDRESS_LINE_2 = VALUES(ADDRESS_LINE_2),
                CITY = VALUES(CITY),
                STATE = VALUES(STATE),
                ZIP_CODE = VALUES(ZIP_CODE),
                COUNTRY = VALUES(COUNTRY)
        """, (
            policy.get("CUSTOMER_ID"), company_id, policy.get("SALUTATION"), policy.get("CUSTOMER_FIRST_NAME"),
            policy.get("CUSTOMER_MIDDLE_NAME"), policy.get("CUSTOMER_LAST_NAME"),
            policy.get("GENDER"), policy.get("DATE_OF_BIRTH"), policy.get("CONTACT_NUMBER"),
            policy.get("EMAIL"), policy.get("CUSTOMER_ADDRESS_LINE_1"), policy.get("CUSTOMER_ADDRESS_LINE_2"),
            policy.get("CUSTOMER_CITY"), policy.get("CUSTOMER_STATE"),
            policy.get("CUSTOMER_ZIP"), policy.get("CUSTOMER_COUNTRY")
        ))


        # ILL_AGENT
        cursor.execute("""INSERT INTO ILL_INSURANCE_AGENT (
            AGENT_ID,INSURANCE_COMPANY_ID, AGENT_CODE, SALUTATION, AGENT_FIRST_NAME, AGENT_MIDDLE_NAME,
            AGENT_LAST_NAME,AGENT_NAME, AGENT_GENDER, AGENT_EMAIL, AGENT_PHONE_NUMBER, AGENT_STATE, AGENT_STATUS
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""", (
            policy.get("AGENT_ID"),company_id, policy.get("AGENT_CODE"), policy.get("AGENT_SALUTATION"),
            policy.get("AGENT_FIRST_NAME"), policy.get("AGENT_MIDDLE_NAME"),
            policy.get("AGENT_LAST_NAME"), policy.get("AGENT_NAME"), policy.get("AGENT_GENDER"),
            policy.get("AGENT_EMAIL"), policy.get("AGENT_PHONE_NUMBER"),
            policy.get("AGENT_STATE"), policy.get("AGENT_STATUS")
        ))

        # ILL_POLICY
        cursor.execute("""INSERT INTO ILL_POLICY (
            POLICY_ID, CUSTOMER_ID, AGENT_ID, INSURANCE_PRODUCT_CODE, POLICY_TYPE,
            POLICY_ISSUED_DATE, POLICY_EXPIRY_DATE, POLICY_STATUS,
            PREMIUM_AMOUNT, FACE_AMOUNT, LOAN_AMOUNT_DISBURESED,
            MINIMUM_INTEREST_RATE_IN_PERCENTAGE, GUARANTEED_INTEREST_RATE_IN_PERCENTAGE,
            CURRENT_INTEREST_RATE_IN_PERCENTAGE, WITHDRAWL_AMOUNT, RIDER_APPLICABLE,
            POLICY_TERM_YEARS, CURRENT_CASH_VALUE
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""", (
            policy_id, policy.get("CUSTOMER_ID"), policy.get("AGENT_ID"), product_code,
            policy.get("POLICY_TYPE"), policy.get("POLICY_ISSUED_DATE"),
            policy.get("POLICY_EXPIRY_DATE"), policy.get("POLICY_STATUS"),
            policy.get("PREMIUM_AMOUNT"), policy.get("FACE_AMOUNT"),
            policy.get("LOAN_AMOUNT_DISBURESED"), policy.get("MINIMUM_INTEREST_RATE_IN_PERCENTAGE"),
            policy.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE"),
            policy.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE"), policy.get("WITHDRAWL_AMOUNT"),
            policy.get("RIDER_APPLICABLE"), policy.get("POLICY_TERM_YEARS"),
            policy.get("CURRENT_CASH_VALUE")
        ))
        
# 🔁 Insert Riders, Beneficiaries, Transactions, Loans & Repayments
        for rider in policy.get("riders", []):
            cursor.execute("""INSERT INTO ILL_RIDER (
                RIDER_ID, POLICY_ID, RIDER_NAME, RIDER_ISSUE_DATE,
                RIDER_COVERAGE_AMOUNT, RIDER_PREMIUM_AMOUNT, RIDER_STATUS
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)""", (
                rider["RIDER_ID"], policy_id, rider.get("RIDER_NAME"),
                rider.get("RIDER_ISSUE_DATE"), rider.get("RIDER_COVERAGE_AMOUNT"),
                rider.get("RIDER_PREMIUM_AMOUNT"), rider.get("RIDER_STATUS")
            ))

        for ben in policy.get("beneficiaries", []):
            cursor.execute("""INSERT INTO ILL_BENEFICIARY (
                BENEFICIARY_ID, POLICY_ID, BENEFICIARY_FIRST_NAME,
                BENEFICIARY_MIDDLE_NAME, BENEFICIARY_LAST_NAME,
                SALUTATION, BENEFICIARY_GENDER, BENEFICIARY_RELATIONSHIP
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""", (
                ben["BENEFICIARY_ID"], policy_id, ben.get("BENEFICIARY_FIRST_NAME"),
                ben.get("BENEFICIARY_MIDDLE_NAME"), ben.get("BENEFICIARY_LAST_NAME"),
                ben.get("SALUTATION"), ben.get("BENEFICIARY_GENDER"),
                ben.get("BENEFICIARY_RELATIONSHIP")
            ))

        for txn in policy.get("transactions", []):
            cursor.execute("""INSERT INTO ILL_TRANSACTION (
                TRANSACTION_ID, POLICY_ID, TRANSACTION_TYPE,
                TRANSACTION_DATE, TRANSACTION_STATUS, TRANSACTION_AMOUNT, TRANSACTION_REQUEST_BY
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)""", (
                txn["TRANSACTION_ID"], policy_id, txn.get("TRANSACTION_TYPE"),
                txn.get("TRANSACTION_DATE"), txn.get("TRANSACTION_STATUS"),
                txn.get("TRANSACTION_AMOUNT"), txn.get("TRANSACTION_REQUEST_BY")
            ))

        for loan in policy.get("loans", []):
            cursor.execute("""INSERT INTO ILL_LOAN (
                LOAN_ID, POLICY_ID, LOAN_AMOUNT, LOAN_ISSUED_DATE,
                LOAN_PERIOD_IN_MONTHS, LOAN_INTEREST_RATE_IN_PERCENTAGE, LOAN_STATUS
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)""", (
                loan["LOAN_ID"], policy_id, loan.get("LOAN_AMOUNT"),
                loan.get("LOAN_ISSUED_DATE"), loan.get("LOAN_PERIOD_IN_MONTHS"),
                loan.get("LOAN_INTEREST_RATE_IN_PERCENTAGE"), loan.get("LOAN_STATUS")
            ))

            for repay in loan.get("repayments", []):
                cursor.execute("""INSERT INTO ILL_SCHEDULE_REPAYMENT (
                    LOAN_ID, SCHEDULE_TYPE, LOAN_SCHEDULED_DUE_DATE,
                    LOAN_ACTUAL_REPAYMENT_DATE, LOAN_REPAYMENT_AMOUNT, STATUS
                ) VALUES (%s, %s, %s, %s, %s, %s)""", (
                    loan["LOAN_ID"], repay.get("SCHEDULE_TYPE"),
                    repay.get("LOAN_SCHEDULED_DUE_DATE"), repay.get("LOAN_ACTUAL_REPAYMENT_DATE"),
                    repay.get("LOAN_REPAYMENT_AMOUNT"), repay.get("STATUS")
                ))

        conn.commit()
        print("✅ All policy-related data inserted successfully.")
        return {"status": "success", "message": f"Policy {policy_id} stored in ILL tables."}

    except Exception as e:
        conn.rollback()
        print(f"❌ Error while inserting: {e}")
        return {"status": "error", "message": str(e)}

    finally:
        cursor.close()
        conn.close()

