from datetime import date
from typing import Optional,List,Union
from pydantic import BaseModel, Field
class PolicySearchRequest(BaseModel):
    customer_name: Optional[str] = Field("", description="Customer's first name for search")
    policy_id: Optional[int]= Field(None,description="Policy ID for search")
    customer_id: Optional[int] = Field(None, description="Customer ID for search")

# One policy response item
class PolicyBasicDetail(BaseModel):
    policyId: int
    customerId: int
    customer_name: str
    policyType: str
    status: str
    maturityDate: date
    faceAmount: float


# Full success response
class PolicyBasicDetailsResponse(BaseModel):
    POLICY_DETAILS: List[PolicyBasicDetail]

class ErrorResponse(BaseModel):
    errorCode: str
    message: str



