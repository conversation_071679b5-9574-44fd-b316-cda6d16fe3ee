from typing import List
from fastapi import HTTPException
from flask import request
from app.models.store_selected_illustrations import SelectedOptionsRequest, SelectedOption, IllustrationScheduleObject
from app.db.connection import get_connection
from datetime import datetime

def store_selected_options(request: SelectedOptionsRequest):
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)  # use dictionary cursor to access by column name



    try:
        # Step 0: Fetch illustration_id and date_of_illustration from ILLUSTRATION table using policy_id
        fetch_illustration_query = """
            SELECT ILLUSTRATION_ID, DATE_OF_ILLUSTRATION
            FROM ILLUSTRATION_TABLE
            WHERE POLICY_ID = %s
            ORDER BY DATE_OF_ILLUSTRATION DESC
            LIMIT 1
        """
        cursor.execute(fetch_illustration_query, (request.selected_options[0].policy_id,))
        row = cursor.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail="No illustration found for given policy_id")

        illustration_id = row["ILLUSTRATION_ID"]
        date_of_illustration = row["DATE_OF_ILLUSTRATION"]

        for option in (request.selected_options):
            # Step 1: Insert first option and get auto-incremented scenario_id
            insert_query = """
                    INSERT INTO ILLUSTRATION_SCENARIO_TABLE (
                        POLICY_ID,
                        ILLUSTRATION_ID,
                        DATE_OF_ILLUSTRATION,
                        ILLUSTRATION_TYPE_ID,
                        ILLUSTRATION_QUESTION_ID,
                        ILLUSTRATION_OPTION_ID,
                        ILLUSTRATION_STARTING_AGE,
                        ILLUSTRATION_ENDING_AGE,
                        NEW_FACE_AMOUNT,
                        NEW_COVERAGE_OPTION,
                        NEW_PREMIUM_AMOUNT,
                        NEW_LOAN_AMOUNT,
                        NEW_LOAN_REPAYMENT_AMOUNT,
                        CURRENT_INTEREST_RATE,
                        GUARANTEED_INTEREST_RATE,
                        ILLUSTRATION_INTEREST_RATE,
                        SURRENDER_AMOUNT,
                        SCHEDULE
                        
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s)
                """
            cursor.execute(insert_query, (
                    option.policy_id,
                    illustration_id,
                    date_of_illustration,
                    option.illustration_type_id,
                    option.illustration_question_id,
                    option.illustration_option_id,
                    option.illustration_starting_age,
                    option.illustration_ending_age,
                    option.new_face_amount,
                    option.new_coverage_option,
                    option.new_premium_amount,
                    option.new_loan_amount,
                    option.new_loan_repayment_amount,
                    option.current_interest_rate,
                    option.guaranteed_interest_rate,
                    option.illustration_interest_rate,
                    option.surrender_amount,
                    option.is_schedule
                ))
            scenario_id = cursor.lastrowid
        conn.commit()
        if option.is_schedule and option.is_schedule.upper() == 'YES':
            if option.schedule_object:
                store_schedule(scenario_id, option.schedule_object)
            else:
                raise HTTPException(status_code=400, detail="Schedule marked as YES but no schedule_object provided.")
        return {"status": "SUCCESS"}
    
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error storing selected options: {str(e)}")

    finally:
        cursor.close()
        conn.close()

    return {"status": "FAILED"}

def store_schedule(scenario_id: int,schedule_object: list[IllustrationScheduleObject]):
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # Step 1: Fetch policy_id and illustration_id from ILLUSTRATION_SCENARIO_TABLE
        fetch_query = """
            SELECT POLICY_ID, ILLUSTRATION_ID
            FROM ILLUSTRATION_SCENARIO_TABLE
            WHERE SCENARIO_ID = %s
        """
        cursor.execute(fetch_query, (scenario_id,))
        record = cursor.fetchone()

        if not record:
            raise HTTPException(status_code=404, detail="Scenario ID not found in ILLUSTRATION_SCENARIO_TABLE")

        policy_id = record["POLICY_ID"]
        illustration_id = record["ILLUSTRATION_ID"]

        # Step 2: Insert into SCHEDULE_TABLE
        insert_query = """
            INSERT INTO ILLUSTRATION_SCHEDULE_TABLE (
                POLICY_ID,
                ILLUSTRATION_ID,
                SCENARIO_ID,
                AGE,
                POLICY_YEAR,
                CURRENT_YEAR,
                FACE_AMOUNT,
                PREMIUM_AMOUNT,
                COVERAGE_OPTION,
                LOAN_AMOUNT,
                SURRENDER_AMOUNT,
                LOAN_REPAYMENT_AMOUNT,
                ILLUSTRATION_INTEREST_RATE
            ) VALUES (%s, %s, %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
        """
        for schedule in schedule_object:
            cursor.execute(insert_query, (
                policy_id,
                illustration_id,
                scenario_id,
                schedule.age,
                schedule.policy_year,
                schedule.current_year,
                schedule.face_amount,
                schedule.premium_amount,
                schedule.coverage_options,
                schedule.loan_amount,
                schedule.surrender_amount,
                schedule.loan_repayment_amount,
                schedule.illustration_interest_rate
            ))

        conn.commit()

    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error in storing schedule: {str(e)}")

    finally:
        cursor.close()
        conn.close()

